import React, { useEffect, useState } from "react";
import { useMediaQuery } from "react-responsive";
import { toast } from "react-toastify";
import { Button, Grid, Image, Message } from "semantic-ui-react";
import MyTextInput from "../../../app/common/form/MyTextInput";
import {
  functionFetchMlsCoCren,
  functionFetchMlsDataFiniti,
  functionFetchMlsIres,
} from "../../../app/firestore/functionsService";
import { useFormikContext } from "formik";
import { useSelector } from "react-redux";
import stateNameToAbbreviation, {
  convertAddressFull,
} from "../../../app/common/util/util";
import MyCheckbox from "../../../app/common/form/MyCheckbox";
import MySelectInput from "../../../app/common/form/MySelectInput";

export default function TransactionImportMlsForm({ mlsData, setMlsData }) {
  const [importMlsStatus, setImportMlsStatus] = useState("Initial");
  const { currentUserProfile } = useSelector((state) => state.profile);
  const [mlsId, setMlsId] = useState(null);
  const { setFieldValue, values } = useFormikContext();
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  const testData1 = {
    address: {
      street: "123 Fortnight St",
      unit: "",
      city: "Brookhaven",
      state: "CO",
      zip: "80002",
    },
    propertyDetails: {
      county: "Denver",
      earnestMoneyHolder: "Title Company",
      exclusions: "Bookcase in 2nd bedroom",
      inclusions: "Fridge, Dishwasher, Oven",
      legalDescription: "some SECTION TOWN 34 AREA BLOCK 1",
      metroDistrictWebsite: "www.denver.gov",
      yearBuilt: "1900",
    },
    listingAgent: {
      firstName: "John",
      lastName: "Boese",
      email: "<EMAIL>",
      phone: "************",
      brokerLicenseNumber: "123456",
      brokerageName: "Demo Realty",
    },
    coListingAgent: {
      firstName: "Elizabeth",
      lastName: "Boese",
      email: "<EMAIL>",
      phone: "************",
      brokerLicenseNumber: "123456",
      brokerageName: "Demo Realty",
    },
    seller: {
      firstName: "Lora",
      lastName: "Ster",
      email: "<EMAIL>",
      phone: "************",
    },
    seller2: {
      firstName: "Esb",
      lastName: "Clientencia",
      email: "<EMAIL>",
      phone: "************",
    },
    pic: "https://construction2style.com/wp-content/uploads/2021/06/photo-001-3.jpeg",
  };
  const testData2 = {
    address: {
      street: "456 Bubbles Ln",
      unit: "",
      city: "Denver",
      state: "CO",
      zip: "80202",
    },
    propertyDetails: {
      county: "Denver",
      earnestMoneyHolder: "Colorado Title Company",
      exclusions: "n/a",
      inclusions: "Clothes Washer, Clothes Dryer, Dishwasher, Oven",
      legalDescription: "Example of a legal descriptions",
      metroDistrictWebsite: "www.denver.gov",
      yearBuilt: "1900",
    },
    listingAgent: {
      firstName: "Elizabeth",
      lastName: "Boese",
      email: "<EMAIL>",
      phone: "************",
      brokerLicenseNumber: "123456",
      brokerageName: "Demo Realty",
    },
    coListingAgent: {
      firstName: "John",
      lastName: "Boese",
      email: "<EMAIL>",
      phone: "************",
      brokerLicenseNumber: "123456",
      brokerageName: "Demo Realty",
    },
    seller: {
      firstName: "Jackie",
      lastName: "Johanssen",
      email: "<EMAIL>",
      phone: "************",
    },
    seller2: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
    },
    pic: "https://readyforwildfire.org/wp-content/uploads/2024/03/Defensible-Space-example-2000x1500-1-1024x768.jpg",
  };

  let mlsOptions = [];
  if (currentUserProfile?.mlsAccess?.length > 0) {
    currentUserProfile.mlsAccess.map((oneMls) => {
      if (oneMls?.mlsIdCode) {
        mlsOptions.push({
          key: oneMls.mlsIdCode,
          value: oneMls.mlsIdCode,
          text: oneMls.mlsName || "MLS",
        });
      }
      return true;
    });
  }

  function mlsImportSuccess(formattedData) {
    setImportMlsStatus("Complete");
    let modifiedMlsData = { ...formattedData };

    if (values?.agentRepresents === "Seller") {
      modifiedMlsData = {
        pic: formattedData.pic,
      };
    } else {
      modifiedMlsData = {
        ...formattedData,
      };
    }
    setFieldValue("mlsDataOptions.useMlsPic", true); // has to be on form element not mlsData
    setFieldValue("mlsDataOptions.addListingAgentToParties", true);

    setMlsData(modifiedMlsData);
    toast.success("MLS data imported successfully");
  }

  // function checkMlsAccess(arrOfMaps, mlsIdCode, valueToCheck) {
  //   for (const map of arrOfMaps) {
  //     // Check if the map contains the key and if its value matches
  //     if (map.has(mlsIdCode) && map.get(mlsIdCode) === valueToCheck) {
  //       return true; // Key-value pair found
  //     }
  //   }
  //   return false; // Key-value pair not found in any map
  // }

  async function handleMlsImport() {
    const mlsIdCodeSelected = values.mlsIdCode;
    console.log(
      "mlsId = ",
      mlsId,
      " mlsIdCodeSelected: ",
      mlsIdCodeSelected,
      " mlsAccess[0].mlsIdCode = ",
      currentUserProfile.mlsAccess?.[0]?.mlsIdCode,
      " mlsAccess[1].mlsIdCode = ",
      currentUserProfile.mlsAccess?.[1]?.mlsIdCode
    );

    if (mlsId) {
      let values = false;
      setImportMlsStatus("Loading");

      if (mlsId === "TA1") {
        values = testData1;
      } else if (mlsId === "TA2") {
        values = testData2;
      }
      if (values) {
        mlsImportSuccess(values);
        return;
      }
      let agentState = "CO";
      if (currentUserProfile.state) {
        agentState = stateNameToAbbreviation(currentUserProfile.state);
      }
      try {
        // MLS CO CREN
        // if (
        //   currentUserProfile.mlsAccess?.length > 0 &&
        //   checkMlsAccess(currentUserProfile.mlsAccess, "mlsIdCode", "CO_CREN")
        // ) {
        if (
          (currentUserProfile.mlsAccess?.length === 1 &&
            currentUserProfile.mlsAccess?.[0]?.mlsIdCode === "CO_CREN") ||
          mlsIdCodeSelected === "CO_CREN"
        ) {
          console.log("Fetching CREN...");
          values = await functionFetchMlsCoCren(mlsId);
          console.log("values: ", values);
          if (values) {
            mlsImportSuccess(values);
            return;
          }
        }

        // MLS CO IRES
        console.log(currentUserProfile.cities);
        if (!currentUserProfile.cities && !values)
          if (
            !currentUserProfile.state ||
            currentUserProfile.state === "Colorado"
          ) {
            if (
              (currentUserProfile.mlsAccess?.length === 1 &&
                currentUserProfile.mlsAccess?.[0]?.mlsIdCode === "CO_IRES") ||
              mlsIdCodeSelected === "CO_IRES" ||
              mlsIdCodeSelected === "CO_RECOLORADO"
            ) {
              console.log("Fetching IRES...");
              values = await functionFetchMlsIres(mlsId);
              if (values) {
                mlsImportSuccess(values);
                return;
              }
            }
          }

        // MLS DataFiniti
        if (!values) {
          console.log("Fetching what we can find not from the mls...");
          values = await functionFetchMlsDataFiniti({
            mlsId: mlsId,
            state: agentState,
          });
          if (values) {
            mlsImportSuccess(values);
          } else {
            setImportMlsStatus("Error");
            toast.error(
              `Error when importing MLS data. Please check the MLS ID and try again or manually add details.`
            );
          }
        }
      } catch (error) {
        setImportMlsStatus("Error");
        toast.error(
          `Error when importing MLS data. Please check the MLS ID and try again or manually add details.`
        );
      }
    } else {
      toast.error("Please add an MLS number");
    }
  }

  function WatcherMlsNum() {
    const { values } = useFormikContext();
    useEffect(() => {
      if (values.mlsNumbers && values.mlsNumbers[0]) {
        setMlsId(values.mlsNumbers[0]);
      }
    }, [values.mlsNumbers]);
    return null;
  }

  useEffect(() => {
    if (!values.mlsNumbers) {
      setFieldValue("mlsNumbers", [""]);
    }
  }, [values.mlsNumbers, setFieldValue]);

  return (
    <>
      <Grid>
        <Grid.Row>
          <Grid.Column mobile={16} computer={4} className="small padding top">
            {currentUserProfile.mlsAccess?.[0]?.mlsName && (
              <MySelectInput
                key="mlsIdCode"
                name="mlsIdCode"
                label="MLS Name"
                options={mlsOptions}
              ></MySelectInput>
            )}
          </Grid.Column>
          <Grid.Column mobile={16} computer={5} className="small padding top">
            <WatcherMlsNum />
            <MyTextInput
              name="mlsNumbers[0]"
              label="MLS #"
              value={
                values.mlsNumbers && values.mlsNumbers[0]
                  ? values.mlsNumbers[0]
                  : ""
              }
            />
          </Grid.Column>
          <Grid.Column mobile={16} computer={4} className="small padding top">
            <Button
              primary
              loading={importMlsStatus === "Loading"}
              disabled={
                importMlsStatus === "Loading" || importMlsStatus === "Complete"
              }
              className={isMobile ? "fluid" : null}
              style={{ marginTop: "20px" }}
              onClick={(e) => {
                e.preventDefault();
                handleMlsImport();
              }}
            >
              {importMlsStatus === "Initial" || importMlsStatus === "Error"
                ? values && values.agentRepresents === "Seller"
                  ? "Import Listing Photo"
                  : "Import MLS Details"
                : `Import ${importMlsStatus}`}
            </Button>
          </Grid.Column>
        </Grid.Row>
        <Grid.Row>
          <Grid.Column mobile={16} computer={16}>
            {!currentUserProfile.mlsAccess && importMlsStatus === "Error" && (
              <>
                <Message negative>
                  <Message.Header>
                    You do not have access to any MLS, and we could not find
                    that MLS number on the internet for your area.
                  </Message.Header>
                  <p>
                    Please contact your administrator to add MLS access for your
                    account, or try again with a different MLS number.
                  </p>
                </Message>
              </>
            )}
            {currentUserProfile.mlsAccess && importMlsStatus === "Error" && (
              <>
                <Message negative>
                  <Message.Header>
                    MLS number not found in your MLS.
                  </Message.Header>
                  <p>Check the number and try again.</p>
                </Message>
              </>
            )}

            {mlsData && !currentUserProfile.mlsAccess && (
              <>
                <Message>
                  <Message.Header>
                    You do not have access to any MLS.
                  </Message.Header>
                  <p>
                    Please contact your administrator to add MLS access for your
                    account.
                  </p>
                  <p>
                    <i>
                      The following information was found on the internet and
                      not from the MLS and may or may not be accurate. Remove
                      any incorrect information.
                    </i>
                  </p>
                </Message>
              </>
            )}
            {/* {mlsData && currentUserProfile.mlsAccess && (
              <Message warning>
                <Message.Header>
                  The MLS import was not found in your MLS, but we found
                  information from the internet.
                </Message.Header>
                <p>
                  <i>
                    The following information was found on the internet and not
                    from the MLS and may or may not be accurate. Remove any
                    incorrect information.
                  </i>
                </p>
              </Message>
            )} */}
          </Grid.Column>
        </Grid.Row>

        {mlsData?.pic && (
          <Grid.Column mobile={16} computer={4}>
            <div className="large bottom margin">
              <Image
                bordered
                rounded
                size="medium"
                wrapped
                src={mlsData?.pic}
              />
              <br />
              <MyCheckbox
                name="mlsDataOptions.useMlsPic"
                label="Import this Picture"
              />
            </div>
          </Grid.Column>
        )}
        {mlsData?.address && (
          <>
            {(!values || values.agentRepresents === "Buyer") && (
              <>
                <Grid.Column mobile={16} computer={4}>
                  <div className="large bottom margin">
                    <h4>Property Details</h4>
                    {mlsData.propertyDetails?.county && (
                      <p className="mini bottom margin">
                        County: {mlsData.propertyDetails?.county}
                      </p>
                    )}
                    {mlsData.propertyDetails?.subdivisionName && (
                      <p className="mini bottom margin">
                        Subdivision: {mlsData.propertyDetails?.subdivisionName}
                      </p>
                    )}
                    {mlsData.propertyDetails?.yearBuilt && (
                      <p className="mini bottom margin">
                        Year Built: {mlsData.propertyDetails?.yearBuilt}
                      </p>
                    )}
                    {mlsData.propertyDetails?.parcelNumber && (
                      <p className="mini bottom margin">
                        Parcel #: {mlsData.propertyDetails?.parcelNumber}
                      </p>
                    )}
                    {mlsData.propertyDetails?.legalDescription && (
                      <p className="mini bottom margin">
                        Legal Description:{" "}
                        {mlsData.propertyDetails?.legalDescription}
                      </p>
                    )}
                  </div>
                </Grid.Column>
                <Grid.Column mobile={16} computer={4}>
                  <div className="large bottom margin">
                    <h4>Listing Agent</h4>
                    <MyCheckbox
                      name="mlsDataOptions.addListingAgentToParties"
                      label="Add Listing Agent to Parties"
                    />
                    <p className="mini bottom margin">
                      Name: {mlsData.listingAgent?.firstName}{" "}
                      {mlsData.listingAgent?.lastName}
                    </p>
                    {mlsData.listingAgent?.email && (
                      <p className="mini bottom margin">
                        Email: {mlsData.listingAgent?.email}
                      </p>
                    )}
                    {mlsData.listingAgent?.phone && (
                      <p className="mini bottom margin">
                        Phone: {mlsData.listingAgent?.phone}
                      </p>
                    )}
                    {mlsData.listingAgent?.brokerLicenseNumber && (
                      <p className="mini bottom margin">
                        License #: {mlsData.listingAgent?.brokerLicenseNumber}
                      </p>
                    )}
                    {mlsData.listingAgent?.brokerageName && (
                      <p className="mini bottom margin">
                        Brokerage: {mlsData.listingAgent?.brokerageName}
                      </p>
                    )}
                    {mlsData.listingAgent?.address?.city && (
                      <p className="mini bottom margin">
                        Address:{" "}
                        {convertAddressFull(mlsData.listingAgent.address)}
                      </p>
                    )}
                    {!mlsData.listingAgent?.email && (
                      <p className="mini bottom margin bold">
                        No email found. Manually add LA in Parties.
                      </p>
                    )}
                  </div>
                </Grid.Column>
              </>
            )}
          </>
        )}
      </Grid>
    </>
  );
}
